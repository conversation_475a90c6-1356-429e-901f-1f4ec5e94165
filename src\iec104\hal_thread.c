/**
 * thread_bsd.c
 *
 * Copyright 2013-2021 <PERSON>
 *
 * This file is part of Platform Abstraction Layer (libpal)
 * for libiec61850, libmms, and lib60870.
 */

#include <pthread.h>
// #include <semaphore.h>
#include <unistd.h>
#include "hal_thread.h"
#include "lib_memory.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "semphr.h"
struct sThread {
    ThreadExecutionFunction function;
    void* parameter;
    TaskHandle_t handle;
    int state;  // 0=created, 1=running, 2=finished
    bool autodestroy;
};

static void
threadFunction(void* parameter)
{
    Thread thread = (Thread)parameter;

    if (thread && thread->function)
        thread->function(thread->parameter);

    // 标记线程已完成
    thread->state = 2; // 2 = finished

    if (thread->autodestroy) {
        GLOBAL_FREEMEM(thread);
    }

    vTaskDelete(NULL);
}


Thread
Thread_create(ThreadExecutionFunction function, void* parameter, bool autodestroy)
{
    if (function == NULL)
        return NULL;

    Thread thread = (Thread) GLOBAL_MALLOC(sizeof(struct sThread));

    if (thread != NULL) {
        thread->parameter = parameter;
        thread->function = function;
        thread->state = 0;  // created
        thread->autodestroy = autodestroy;
        thread->handle = NULL;
    }

    return thread;
}

void
Thread_start(Thread thread)
{
    BaseType_t result;

    if (thread == NULL || thread->state != 0)
        return;

    const char* taskName = thread->autodestroy ? "autoThread" : "manualThread";

    result = xTaskCreate(threadFunction, taskName,
        4096, thread, tskIDLE_PRIORITY + 1, &thread->handle);

    if (result == pdPASS) {
        thread->state = 1;  // running
    }
}

void
Thread_destroy(Thread thread)
{
    if (thread == NULL)
        return;

    // 自动销毁的线程不应该手动销毁，因为它们会在 threadFunction 中自己调用 vTaskDelete(NULL)
    if (thread->autodestroy)
        return;

    GLOBAL_FREEMEM(thread);
}


void
Thread_sleep(int millies)
{
    vTaskDelay(pdMS_TO_TICKS(millies));
}

Semaphore
Semaphore_create(int initialValue)
{
    SemaphoreHandle_t sem = xSemaphoreCreateCounting(0x7fff, initialValue);
    return (Semaphore)sem;
}

void
Semaphore_wait(Semaphore self)
{
    xSemaphoreTake((SemaphoreHandle_t)self, portMAX_DELAY);
}

void
Semaphore_post(Semaphore self)
{
    xSemaphoreGive((SemaphoreHandle_t)self);
}

void
Semaphore_destroy(Semaphore self)
{
    vSemaphoreDelete((SemaphoreHandle_t)self);
}

