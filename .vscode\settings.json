{"files.associations": {"*.cpp": "cpp", "new": "cpp", "array": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "string_view": "cpp", "initializer_list": "cpp", "*.tcc": "cpp", "memory": "cpp", "random": "cpp", "ethernet.h": "c", "utility": "cpp", "lib_memory.h": "c", "task.h": "c", "lib60870_config.h": "c", "arduino.h": "c", "hardwareserial.h": "c"}, "idf.portWin": "COM32"}