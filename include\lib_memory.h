/*
 *  lib_memory.h
 *
 *  Copyright 2024
 *
 *  Memory management functions for lib60870
 */

#ifndef LIB_MEMORY_H_
#define LIB_MEMORY_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdlib.h>

// Memory allocation functions
#define GLOBAL_MALLOC(size) malloc(size)
#define GLOBAL_CALLOC(num, size) calloc(num, size)
#define GLOBAL_FREEMEM(ptr) free(ptr)

#ifdef __cplusplus
}
#endif

#endif /* LIB_MEMORY_H_ */
