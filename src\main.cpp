/*
*  w5500_example.c
*
*  Copyright 2024
*
*  Example usage of W5500 socket implementation for lib60870
*/

#include <Arduino.h>
#include "Ethernet.h"
#include <SPI.h>
#include <sys/signal.h>
#include "iec104\lib60870_config.h"
#include "iec104\hal_socket.h"
#include "iec104\iec60870_slave.h"
#include "iec104\cs104_slave.h"
#include "iec104\hal_time.h"
#include "iec104\iec60870_common.h"
#include "iec104\cs104_connection.h"

#define ETH_MISO 13
#define ETH_SCK 12
#define ETH_MOSI 11
#define ETH_CS 10
// #define ETH_RST 15 // 9 gateway
#define ETH_RST 9 // 15 kaifaban
#define ETH_INT 14

#define PIN_SPI_SS ETH_CS
#define PIN_ETHERNET_RESET ETH_RST

// Initialize Ethernet hardware
void initializeEthernet() {
    pinMode(PIN_ETHERNET_RESET, OUTPUT);
    digitalWrite(PIN_ETHERNET_RESET, LOW);
    delay(100);
    digitalWrite(PIN_ETHERNET_RESET, HIGH);

    Ethernet.init(PIN_SPI_SS);
    SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
    Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d\n", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
    SPI.setFrequency(8000000); // 提高SPI频率到8MHz
}

void getMacAddr(uint8_t *dmac) {
    assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
}

// Start Ethernet connection with DHCP
bool startEthernetConnection() {
    uint8_t mac[6];
    getMacAddr(mac);
    mac[0] &= 0xfe;

    Serial0.printf("Start Ethernet DHCP\n");
    int status = Ethernet.begin(mac);

    return (status != 0);
}

void handleEthernetError() {
    if (Ethernet.hardwareStatus() == EthernetNoHardware) {
        Serial0.printf("Ethernet shield was not found\n");
    } else if (Ethernet.linkStatus() == LinkOFF) {
        Serial0.printf("Ethernet cable is not connected\n");
    } else {
        Serial0.printf("Unknown Ethernet error\n");
    }
}


static bool running = true;

void
sigint_handler(int signalId)
{
    running = false;
}

void
printCP56Time2a(CP56Time2a time)
{
    Serial0.printf("%02i:%02i:%02i %02i/%02i/%04i", CP56Time2a_getHour(time),
                            CP56Time2a_getMinute(time),
                            CP56Time2a_getSecond(time),
                            CP56Time2a_getDayOfMonth(time),
                            CP56Time2a_getMonth(time),
                            CP56Time2a_getYear(time) + 2000);
}



/* Callback handler to log sent or received messages (optional) */
static void
rawMessageHandler(void* parameter, IMasterConnection conneciton, uint8_t* msg, int msgSize, bool sent)
{
    if (sent)
        Serial0.printf("SEND: ");
    else
        Serial0.printf("RCVD: ");

    int i;
    for (i = 0; i < msgSize; i++) {
        Serial0.printf("%02x ", msg[i]);
    }

    Serial0.printf("\n");
}


static bool
clockSyncHandler (void* parameter, IMasterConnection connection, CS101_ASDU asdu, CP56Time2a newTime)
{
    Serial0.printf("Process time sync command with time "); printCP56Time2a(newTime); Serial0.printf("\n");

    uint64_t newSystemTimeInMs = CP56Time2a_toMsTimestamp(newTime);

    /* Set time for ACT_CON message */
    CP56Time2a_setFromMsTimestamp(newTime, Hal_getTimeInMs());

    /* update system time here */

    return true;
}

static bool
interrogationHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu, uint8_t qoi)
{
Serial0.printf("Received interrogation for group %i\n", qoi);

if (qoi == 20) /* only handle station interrogation */
{
    CS101_AppLayerParameters alParams = IMasterConnection_getApplicationLayerParameters(connection);

    IMasterConnection_sendACT_CON(connection, asdu, false);

    /* The CS101 specification only allows information objects without timestamp in GI responses */

    CS101_ASDU newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_INTERROGATED_BY_STATION,
            0, 1, false, false);

    InformationObject io = (InformationObject) MeasuredValueScaled_create(NULL, 100, -1, IEC60870_QUALITY_GOOD);

    CS101_ASDU_addInformationObject(newAsdu, io);

    CS101_ASDU_addInformationObject(newAsdu, (InformationObject)
        MeasuredValueScaled_create((MeasuredValueScaled) io, 101, 23, IEC60870_QUALITY_GOOD));

    CS101_ASDU_addInformationObject(newAsdu, (InformationObject)
        MeasuredValueScaled_create((MeasuredValueScaled) io, 102, 2300, IEC60870_QUALITY_GOOD));

    InformationObject_destroy(io);

    IMasterConnection_sendASDU(connection, newAsdu);

    CS101_ASDU_destroy(newAsdu);

    newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_INTERROGATED_BY_STATION,
                0, 1, false, false);

    io = (InformationObject) SinglePointInformation_create(NULL, 104, true, IEC60870_QUALITY_GOOD);

    CS101_ASDU_addInformationObject(newAsdu, io);

    CS101_ASDU_addInformationObject(newAsdu, (InformationObject)
        SinglePointInformation_create((SinglePointInformation) io, 105, false, IEC60870_QUALITY_GOOD));

    InformationObject_destroy(io);

    IMasterConnection_sendASDU(connection, newAsdu);

    CS101_ASDU_destroy(newAsdu);

    newAsdu = CS101_ASDU_create(alParams, true, CS101_COT_INTERROGATED_BY_STATION,
            0, 1, false, false);

    CS101_ASDU_addInformationObject(newAsdu, io = (InformationObject) SinglePointInformation_create(NULL, 300, true, IEC60870_QUALITY_GOOD));
    CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 301, false, IEC60870_QUALITY_GOOD));
    CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 302, true, IEC60870_QUALITY_GOOD));
    CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 303, false, IEC60870_QUALITY_GOOD));
    CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 304, true, IEC60870_QUALITY_GOOD));
    CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 305, false, IEC60870_QUALITY_GOOD));
    CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 306, true, IEC60870_QUALITY_GOOD));
    CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 307, false, IEC60870_QUALITY_GOOD));

    InformationObject_destroy(io);

    IMasterConnection_sendASDU(connection, newAsdu);

    CS101_ASDU_destroy(newAsdu);

    newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_INTERROGATED_BY_STATION,
                    0, 1, false, false);

    io = (InformationObject) BitString32_create(NULL, 500, 0xaaaa);

    CS101_ASDU_addInformationObject(newAsdu, io);

    InformationObject_destroy(io);

    IMasterConnection_sendASDU(connection, newAsdu);

    CS101_ASDU_destroy(newAsdu);

    IMasterConnection_sendACT_TERM(connection, asdu);
}
else
{
    IMasterConnection_sendACT_CON(connection, asdu, true);
}

return true;
}


static bool
asduHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu)
{
if (CS101_ASDU_getTypeID(asdu) == C_SC_NA_1) {
    Serial0.printf("received single command\n");

    if  (CS101_ASDU_getCOT(asdu) == CS101_COT_ACTIVATION) {
        InformationObject io = CS101_ASDU_getElement(asdu, 0);

        if (io) {
            if (InformationObject_getObjectAddress(io) == 5000) {
                SingleCommand sc = (SingleCommand) io;

                Serial0.printf("IOA: %i switch to %i\n", InformationObject_getObjectAddress(io),
                        SingleCommand_getState(sc));

                CS101_ASDU_setCOT(asdu, CS101_COT_ACTIVATION_CON);
            }
            else
                CS101_ASDU_setCOT(asdu, CS101_COT_UNKNOWN_IOA);

            InformationObject_destroy(io);
        }
        else {
            Serial0.printf("ERROR: message has no valid information object\n");
            return true;
        }
    }
    else
        CS101_ASDU_setCOT(asdu, CS101_COT_UNKNOWN_COT);

    IMasterConnection_sendASDU(connection, asdu);

    return true;
}

return false;
}

static bool
connectionRequestHandler(void* parameter, const char* ipAddress)
{
    Serial0.printf("New connection request from %s\n", ipAddress);

    #if 0
    if (strcmp(ipAddress, "127.0.0.1") == 0) {
        Serial0.printf("Accept connection\n");
        return true;
    }
    else {
        Serial0.printf("Deny connection\n");
        return false;
    }
    #else
    return true;
    #endif
}


static bool connected = false;

static void
connectionEventHandler(void* parameter, IMasterConnection con, CS104_PeerConnectionEvent event)
{
    if (event == CS104_CON_EVENT_CONNECTION_OPENED) {
        Serial0.printf("Connection opened (%p)\n", con);
        connected = true;
    }
    else if (event == CS104_CON_EVENT_CONNECTION_CLOSED) {
        Serial0.printf("Connection closed (%p)\n", con);
    }
    else if (event == CS104_CON_EVENT_ACTIVATED) {
        Serial0.printf("Connection activated (%p)\n", con);
    }
    else if (event == CS104_CON_EVENT_DEACTIVATED) {
        Serial0.printf("Connection deactivated (%p)\n", con);
    }
}
// Print network information
void printNetworkInfo() {
    Serial0.printf("Local IP %u.%u.%u.%u\n", Ethernet.localIP()[0], Ethernet.localIP()[1], Ethernet.localIP()[2], Ethernet.localIP()[3]);
    Serial0.printf("Subnet Mask %u.%u.%u.%u\n", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], Ethernet.subnetMask()[2], Ethernet.subnetMask()[3]);
    Serial0.printf("Gateway IP %u.%u.%u.%u\n", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], Ethernet.gatewayIP()[2], Ethernet.gatewayIP()[3]);
    Serial0.printf("DNS Server IP %u.%u.%u.%u\n", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], Ethernet.dnsServerIP()[2], Ethernet.dnsServerIP()[3]);
    Serial0.printf("Ethernet connection successful!\n");
}



// Arduino required functions
void setup() {
    // Initialize serial communication
    Serial0.begin(115200);
    while (!Serial0) {
        ; // wait for serial port to connect
    }

    Serial0.println("ESP32 W5500 Socket Example");
    Serial0.println("Starting initialization...");

    initializeEthernet();

    if (!startEthernetConnection()) {
    handleEthernetError();
    return;
    }

    printNetworkInfo();

    /* create a new slave/server instance with default connection parameters and
    * default message queue size */
    CS104_Slave slave = CS104_Slave_create(4, 10);

    IPAddress ip = Ethernet.localIP();
    char ipStr[16];  // xxx.xxx.xxx.xxx 最长15字符 + 终止符
    snprintf(ipStr, sizeof(ipStr), "%u.%u.%u.%u", ip[0], ip[1], ip[2], ip[3]);
    CS104_Slave_setLocalAddress(slave, ipStr);

    /* Set mode to a single redundancy group
    * NOTE: library has to be compiled with CONFIG_CS104_SUPPORT_SERVER_MODE_SINGLE_REDUNDANCY_GROUP enabled (=1)
    */
    CS104_Slave_setServerMode(slave, CS104_MODE_SINGLE_REDUNDANCY_GROUP);

    /* get the connection parameters - we need them to create correct ASDUs -
    * you can also modify the parameters here when default parameters are not to be used */
    CS101_AppLayerParameters alParams = CS104_Slave_getAppLayerParameters(slave);

    /* when you have to tweak the APCI parameters (t0-t3, k, w) you can access them here */
    CS104_APCIParameters apciParams = CS104_Slave_getConnectionParameters(slave);

    Serial0.printf("APCI parameters:\n");
    Serial0.printf("  t0: %i\n", apciParams->t0);
    Serial0.printf("  t1: %i\n", apciParams->t1);
    Serial0.printf("  t2: %i\n", apciParams->t2);
    Serial0.printf("  t3: %i\n", apciParams->t3);
    Serial0.printf("  k: %i\n", apciParams->k);
    Serial0.printf("  w: %i\n", apciParams->w);

    /* set the callback handler for the clock synchronization command */
    CS104_Slave_setClockSyncHandler(slave, clockSyncHandler, NULL);
    /* set the callback handler for the interrogation command */
    CS104_Slave_setInterrogationHandler(slave, interrogationHandler, NULL);
    /* set handler for other message types */
    CS104_Slave_setASDUHandler(slave, asduHandler, NULL);
    /* set handler to handle connection requests (optional) */
    CS104_Slave_setConnectionRequestHandler(slave, connectionRequestHandler, NULL);
    /* set handler to track connection events (optional) */
    CS104_Slave_setConnectionEventHandler(slave, connectionEventHandler, NULL);
    /* uncomment to log messages */
    //CS104_Slave_setRawMessageHandler(slave, rawMessageHandler, NULL);

    CS104_Slave_start(slave);
    if (CS104_Slave_isRunning(slave) == false) {
    Serial0.printf("Starting server failed!\n");
    CS104_Slave_destroy(slave);
    delay(500);
    }

    int16_t scaledValue = 0;

    while (running)
    {
    delay(1000);
    Serial0.println("loop");
    CS101_ASDU newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_PERIODIC, 0, 1, false, false);

    InformationObject io = (InformationObject) MeasuredValueScaled_create(NULL, 110, scaledValue, IEC60870_QUALITY_GOOD);

    scaledValue++;

    CS101_ASDU_addInformationObject(newAsdu, io);

    InformationObject_destroy(io);

    /* Add ASDU to slave event queue */
    CS104_Slave_enqueueASDU(slave, newAsdu);

    CS101_ASDU_destroy(newAsdu);
    }
    delay(1000);
    Serial0.println("Stopping server\n");
    CS104_Slave_stop(slave);
    CS104_Slave_destroy(slave);
    delay(500);
}

void loop() {
    // Main loop - you can add your main application logic here
    delay(1000);
}
