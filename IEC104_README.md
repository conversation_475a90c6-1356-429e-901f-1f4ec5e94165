# IEC104Server 类使用说明

## 概述

`IEC104Server` 是一个封装了 IEC 60870-5-104 协议功能的 C++ 类，专为 ESP32 平台设计。该类将原本混合在 main.cpp 中的 IEC104 功能独立出来，使代码更加模块化和易于维护。

## 文件结构

- `src/IEC104Server.h` - 头文件，包含类定义和接口
- `src/IEC104Server.cpp` - 源文件，包含类的实现
- `src/main_example.cpp` - 使用示例

## 主要功能

### 1. 服务器管理
- 初始化 IEC104 服务器
- 启动和停止服务器
- 连接状态监控
- 连接事件处理

### 2. 数据发送
- 发送周期性数据
- 发送测量值 (Measured Values)
- 发送单点信息 (Single Point Information)
- 发送位串数据 (Bit String 32)

### 3. 协议处理
- 时钟同步处理
- 总召唤处理
- 单点控制命令处理
- 连接请求处理

## 使用方法

### 1. 基本初始化

```cpp
#include "IEC104Server.h"

IEC104Server iec104Server;

void setup() {
    // 首先初始化网络连接 (W5500, WiFi 等)
    // ...
    
    // 获取本地IP地址
    char ipStr[16] = "*************";  // 替换为实际IP
    
    // 初始化IEC104服务器
    if (!iec104Server.initialize(ipStr, 4, 10)) {
        Serial.println("Failed to initialize IEC104 server");
        return;
    }
    
    // 启动服务器
    if (!iec104Server.start()) {
        Serial.println("Failed to start IEC104 server");
        return;
    }
}
```

### 2. 发送数据

```cpp
void loop() {
    // 检查连接状态
    if (iec104Server.isConnected()) {
        // 发送测量值
        iec104Server.sendMeasuredValue(100, 1234);
        
        // 发送单点信息
        iec104Server.sendSinglePointInfo(200, true);
        
        // 发送位串
        iec104Server.sendBitString32(300, 0x12345678);
        
        // 发送周期性数据
        iec104Server.sendPeriodicData();
    }
    
    delay(1000);
}
```

### 3. 高级配置

```cpp
// 启用原始消息日志记录
iec104Server.enableRawMessageLogging(true);

// 设置服务器模式
iec104Server.setServerMode(CS104_MODE_SINGLE_REDUNDANCY_GROUP);

// 获取应用层参数
CS101_AppLayerParameters alParams = iec104Server.getAppLayerParameters();

// 获取连接参数
CS104_APCIParameters apciParams = iec104Server.getConnectionParameters();
```

## API 参考

### 构造函数和析构函数
- `IEC104Server()` - 构造函数
- `~IEC104Server()` - 析构函数，自动清理资源

### 配置方法
- `bool initialize(const char* localIP, int maxConnections = 4, int messageQueueSize = 10)` - 初始化服务器
- `void setServerMode(CS104_ServerMode mode)` - 设置服务器模式
- `void enableRawMessageLogging(bool enable)` - 启用/禁用原始消息日志

### 控制方法
- `bool start()` - 启动服务器
- `void stop()` - 停止服务器
- `bool isRunning()` - 检查服务器是否运行
- `bool isConnected()` - 检查是否有客户端连接

### 数据发送方法
- `void sendPeriodicData()` - 发送周期性数据
- `void sendMeasuredValue(int ioa, int16_t value, QualityDescriptor quality = IEC60870_QUALITY_GOOD)` - 发送测量值
- `void sendSinglePointInfo(int ioa, bool value, QualityDescriptor quality = IEC60870_QUALITY_GOOD)` - 发送单点信息
- `void sendBitString32(int ioa, uint32_t value)` - 发送32位位串

### 实用方法
- `void process()` - 处理方法（可在主循环中调用）
- `CS101_AppLayerParameters getAppLayerParameters()` - 获取应用层参数
- `CS104_APCIParameters getConnectionParameters()` - 获取连接参数

## 回调处理

该类自动处理以下 IEC104 协议事件：

1. **时钟同步** - 自动响应时钟同步命令
2. **总召唤** - 响应总召唤命令，发送预定义的数据点
3. **单点控制** - 处理单点控制命令
4. **连接管理** - 管理客户端连接和断开事件

## 注意事项

1. **网络初始化** - 在使用 IEC104Server 之前，必须先初始化网络连接（W5500、WiFi等）
2. **内存管理** - 类会自动管理 IEC104 相关的内存资源
3. **线程安全** - 该类不是线程安全的，应在单线程环境中使用
4. **错误处理** - 所有方法都包含适当的错误检查和处理

## 示例项目

参考 `src/main_example.cpp` 文件查看完整的使用示例，该示例展示了：
- 网络初始化
- IEC104 服务器设置
- 周期性数据发送
- 连接状态监控

## 依赖项

- lib60870 库（IEC 60870-5-104 协议实现）
- Arduino 框架
- 网络库（如 Ethernet 库用于 W5500）
